<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>arXiv Research Papers</title>
<style>
  body {
    margin: 0;
    font-family: system-ui, sans-serif;
    background: #f8f9fb;
    color: #1a1a1a;
  }
  header {
    text-align: center;
    padding: 2rem 1rem 1rem;
  }
  header h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
  }
  header p {
    margin: 0.5rem 0 0;
    color: #666;
    font-size: 1rem;
  }

  .filters {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
    background: #fff;
    padding: 1rem;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    max-width: 1100px;
    margin: 1rem auto 2rem;
  }
  .filters input[type="text"],
  .filters input[type="date"],
  .filters select {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    width: 100%;
  }
  .filters .toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
  }
  .filters label {
    font-weight: 500;
  }

  .paper-list {
    max-width: 1100px;
    margin: auto;
    padding: 0 1rem 2rem;
  }
  .paper-card {
    background: #fff;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  }
  .paper-header {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }
  .paper-title {
    font-size: 1.1rem;
    font-weight: 600;
    flex: 1 1 auto;
  }
  .badge {
    display: inline-block;
    background: #eef2ff;
    color: #3346d3;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    margin-right: 0.25rem;
  }
  .notable {
    background: #e6f7ec;
    color: #2d8a45;
  }
  .date-badge {
    background: #f0f0f0;
    color: #444;
  }
  .paper-summary {
    font-size: 0.9rem;
    margin: 0.5rem 0 1rem;
    color: #333;
  }
  .paper-meta {
    font-size: 0.85rem;
    color: #555;
    margin-top: 0.5rem;
  }
  .paper-link {
    text-align: right;
    margin-top: -2.5rem;
  }
  .paper-link a {
    background: #0066cc;
    color: #fff;
    text-decoration: none;
    font-size: 0.85rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
  }
</style>
</head>
<body>

<header>
  <h1>arXiv Research Papers</h1>
  <p>Explore and filter extracted research papers from arXiv</p>
</header>

<div class="filters">
  <input type="text" id="searchInput" placeholder="Search papers, authors...">
  <input type="date" id="dateFilter">
  <select id="categoryFilter"><option value="">All categories</option></select>
  <div class="toggle">
    <input type="checkbox" id="notableFilter">
    <label for="notableFilter">Notable Authors Only</label>
  </div>
</div>

<div class="paper-list" id="paperList"></div>

<script>
// Example dataset
const data = {
  "date": "2024-08-25",
  "papers": [
    {
      "id": "2409.08281v1",
      "abs_url": "https://arxiv.org/abs/2409.08281",
      "title": "StockTime: A Time Series Specialized Large Language Model Architecture for Stock Price Prediction",
      "summary": "The stock price prediction task holds a significant role in the financial domain and has been studied for a long time...",
      "published": "2024-08-25",
      "authors": ["Shengkun Wang","Taoran Ji","Linhan Wang","Yanshen Sun","Shang-Ching Liu","Amit Kumar","Chang-Tien Lu"],
      "categories": ["q-fin.ST","cs.AI","cs.CE","cs.LG"],
      "affiliations": { "has_notable_affiliations": true }
    },
    {
      "id": "2409.08282v1",
      "abs_url": "https://arxiv.org/abs/2409.08282",
      "title": "Deep Learning Approaches for Natural Language Processing in Financial Markets",
      "summary": "This paper presents a comprehensive survey of deep learning techniques applied to financial NLP...",
      "published": "2024-08-24",
      "authors": ["Alice Johnson","Bob Smith","Charlie Brown"],
      "categories": ["cs.LG","cs.CL","q-fin.CP"],
      "affiliations": { "has_notable_affiliations": false }
    }
  ]
};

// Populate category dropdown
const categorySet = new Set();
data.papers.forEach(p => p.categories.forEach(c => categorySet.add(c)));
const categoryFilter = document.getElementById('categoryFilter');
Array.from(categorySet).sort().forEach(cat => {
  const opt = document.createElement('option');
  opt.value = cat;
  opt.textContent = cat;
  categoryFilter.appendChild(opt);
});

function renderPapers() {
  const list = document.getElementById('paperList');
  list.innerHTML = '';
  const search = document.getElementById('searchInput').value.toLowerCase();
  const date = document.getElementById('dateFilter').value;
  const cat = categoryFilter.value;
  const notable = document.getElementById('notableFilter').checked;

  const filtered = data.papers.filter(p => {
    if (search) {
      const text = p.title.toLowerCase() + p.authors.join(" ").toLowerCase();
      if (!text.includes(search)) return false;
    }
    if (date && p.published !== date) return false;
    if (cat && !p.categories.includes(cat)) return false;
    if (notable && !p.affiliations.has_notable_affiliations) return false;
    return true;
  });

  filtered.forEach(p => {
    const card = document.createElement('div');
    card.className = 'paper-card';

    const header = document.createElement('div');
    header.className = 'paper-header';

    const title = document.createElement('div');
    title.className = 'paper-title';
    title.textContent = p.title;

    const cats = document.createElement('div');
    p.categories.forEach(c => {
      const b = document.createElement('span');
      b.className = 'badge';
      b.textContent = c;
      cats.appendChild(b);
    });

    if (p.affiliations.has_notable_affiliations) {
      const n = document.createElement('span');
      n.className = 'badge notable';
      n.textContent = 'Notable Authors';
      cats.appendChild(n);
    }

    const d = document.createElement('span');
    d.className = 'badge date-badge';
    d.textContent = p.published;
    cats.appendChild(d);

    header.appendChild(title);
    header.appendChild(cats);
    card.appendChild(header);

    const summary = document.createElement('div');
    summary.className = 'paper-summary';
    summary.textContent = p.summary;
    card.appendChild(summary);

    const authors = document.createElement('div');
    authors.className = 'paper-meta';
    authors.innerHTML = `<strong>Authors: <AUTHORS>
    card.appendChild(authors);

    const categories = document.createElement('div');
    categories.className = 'paper-meta';
    categories.innerHTML = `<strong>Categories:</strong> ${p.categories.join(', ')}`;
    card.appendChild(categories);

    const link = document.createElement('div');
    link.className = 'paper-link';
    link.innerHTML = `<a href="${p.abs_url}" target="_blank">View on arXiv</a>`;
    card.appendChild(link);

    list.appendChild(card);
  });
}

// Event listeners
document.getElementById('searchInput').addEventListener('input', renderPapers);
document.getElementById('dateFilter').addEventListener('change', renderPapers);
document.getElementById('categoryFilter').addEventListener('change', renderPapers);
document.getElementById('notableFilter').addEventListener('change', renderPapers);

renderPapers();
</script>

</body>
</html>
