<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>arXiv Research Papers</title>
<style>
  :root {
    --bg-primary: #f8f9fb;
    --bg-secondary: #fff;
    --text-primary: #1a1a1a;
    --text-secondary: #333;
    --text-muted: #666;
    --text-meta: #555;
    --border-color: #ddd;
    --shadow: rgba(0,0,0,0.05);
    --badge-bg: #eef2ff;
    --badge-text: #3346d3;
    --notable-bg: #e6f7ec;
    --notable-text: #2d8a45;
    --notable-border: #28a745;
    --date-badge-bg: #f0f0f0;
    --date-badge-text: #444;
    --link-bg: #0066cc;
    --affiliations-bg: #f8f9fa;
  }

  [data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --text-primary: #e0e0e0;
    --text-secondary: #d0d0d0;
    --text-muted: #a0a0a0;
    --text-meta: #b0b0b0;
    --border-color: #404040;
    --shadow: rgba(0,0,0,0.3);
    --badge-bg: #2a3441;
    --badge-text: #7c9bd9;
    --notable-bg: #1e3a28;
    --notable-text: #6bc77a;
    --notable-border: #4caf50;
    --date-badge-bg: #3a3a3a;
    --date-badge-text: #c0c0c0;
    --link-bg: #4a90e2;
    --affiliations-bg: #2a2a2a;
  }

  body {
    margin: 0;
    font-family: system-ui, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
  }
  header {
    text-align: center;
    padding: 2rem 1rem 1rem;
    position: relative;
  }
  header h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
  }
  header p {
    margin: 0.5rem 0 0;
    color: var(--text-muted);
    font-size: 1rem;
  }

  .theme-toggle {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-primary);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .theme-toggle:hover {
    background: var(--badge-bg);
    color: var(--badge-text);
  }

  .theme-icon {
    font-size: 1rem;
  }

  @media (max-width: 768px) {
    .theme-toggle {
      position: static;
      margin: 0 auto 1rem;
      display: flex;
      width: fit-content;
    }

    header {
      padding: 1rem;
    }

    .filters {
      flex-direction: column;
      align-items: stretch;
    }

    .filters input,
    .filters select {
      width: auto;
    }
  }

  .filters {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: 12px;
    box-shadow: 0 1px 3px var(--shadow);
    max-width: 1100px;
    margin: 1rem auto 2rem;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
  }
  .filters input[type="text"],
  .filters input[type="date"],
  .filters select {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    width: 100%;
    background: var(--bg-secondary);
    color: var(--text-primary);
    transition: all 0.3s ease;
  }
  .filters input[type="text"]:focus,
  .filters input[type="date"]:focus,
  .filters select:focus {
    outline: none;
    border-color: var(--badge-text);
    box-shadow: 0 0 0 2px var(--badge-bg);
  }
  .filters .toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
  }
  .filters label {
    font-weight: 500;
  }

  .paper-list {
    max-width: 1100px;
    margin: auto;
    padding: 0 1rem 2rem;
  }
  .paper-card {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 1px 3px var(--shadow);
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
  }
  .paper-header {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }
  .paper-title {
    font-size: 1.1rem;
    font-weight: 600;
    flex: 1 1 auto;
  }
  .badge {
    display: inline-block;
    background: var(--badge-bg);
    color: var(--badge-text);
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    margin-right: 0.25rem;
    transition: all 0.3s ease;
  }
  .notable {
    background: var(--notable-bg);
    color: var(--notable-text);
  }
  .date-badge {
    background: var(--date-badge-bg);
    color: var(--date-badge-text);
  }
  .paper-summary {
    font-size: 0.9rem;
    margin: 0.5rem 0 1rem;
    color: var(--text-secondary);
  }
  .paper-meta {
    font-size: 0.85rem;
    color: var(--text-meta);
    margin-top: 0.5rem;
  }
  .paper-link {
    text-align: right;
    margin-top: -2.5rem;
  }
  .paper-link a {
    background: var(--link-bg);
    color: #fff;
    text-decoration: none;
    font-size: 0.85rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    transition: background-color 0.3s ease;
  }
  .paper-link a:hover {
    opacity: 0.9;
  }
  .notable-affiliations {
    background: var(--affiliations-bg);
    border-left: 3px solid var(--notable-border);
    padding: 0.5rem;
    margin: 0.5rem 0;
    border-radius: 4px;
    transition: all 0.3s ease;
  }
  .notable-affiliations strong {
    color: var(--notable-border);
  }
</style>
</head>
<body>

<header>
  <button class="theme-toggle" id="themeToggle">
    <span class="theme-icon" id="themeIcon">🌙</span>
    <span id="themeText">Dark Mode</span>
  </button>
  <h1>arXiv Research Papers</h1>
  <p>Explore and filter extracted research papers from arXiv</p>
</header>

<div class="filters">
  <input type="text" id="searchInput" placeholder="Search papers, authors...">
  <input type="date" id="dateFilter">
  <select id="categoryFilter"><option value="">All categories</option></select>
  <div class="toggle">
    <input type="checkbox" id="notableFilter">
    <label for="notableFilter">Notable Authors Only</label>
  </div>
</div>

<div class="paper-list" id="paperList"></div>

<script>
// Load and combine all paper data from the data directory
let allPapers = [];
let allCategories = new Set();
let allNotableAffiliations = new Set();

// Function to load data from JSON files
async function loadPaperData() {
  try {
    console.log('Loading data file list...');
    // Get list of all JSON files in the data directory
    const response = await fetch('data_files.json');
    const dataFiles = await response.json();
    console.log('Found data files:', dataFiles);

    for (const file of dataFiles) {
      try {
        const fileResponse = await fetch(file);
        const fileData = await fileResponse.json();

        if (fileData.papers && Array.isArray(fileData.papers)) {
          // Process each paper
          fileData.papers.forEach(paper => {
            // Format the paper data for the visualization
            const formattedPaper = {
              id: paper.id,
              abs_url: paper.abs_url,
              title: paper.title,
              summary: paper.summary,
              published: paper.published ? paper.published.split('T')[0] : 'Unknown', // Extract date part
              authors: paper.authors || [],
              categories: paper.categories || [],
              affiliations: paper.affiliations || { has_notable_affiliations: false },
              notable_affiliations_list: []
            };

            // Extract notable affiliations organizations (deduplicated per paper)
            if (paper.affiliations && paper.affiliations.notable_affiliations) {
              const paperAffiliations = new Set();
              paper.affiliations.notable_affiliations.forEach(affiliation => {
                if (affiliation.organization) {
                  paperAffiliations.add(affiliation.organization);
                  allNotableAffiliations.add(affiliation.organization);
                }
              });
              formattedPaper.notable_affiliations_list = Array.from(paperAffiliations);
            }

            // Add categories to the global set
            if (paper.categories) {
              paper.categories.forEach(cat => allCategories.add(cat));
            }

            allPapers.push(formattedPaper);
          });
        }
      } catch (error) {
        console.warn(`Failed to load ${file}:`, error);
      }
    }

    console.log(`Loaded ${allPapers.length} papers from ${dataFiles.length} files`);
    console.log(`Found ${allCategories.size} categories and ${allNotableAffiliations.size} notable affiliations`);

    // Initialize the UI after data is loaded
    initializeUI();
    renderPapers();

  } catch (error) {
    console.error('Failed to load paper data:', error);
    // Fallback to empty data
    initializeUI();
    renderPapers();
  }
}

// Fallback: if PHP script is not available, try to load known files directly
async function loadPaperDataFallback() {
  const knownFiles = [
    'data/cs.AI/24/08/25/papers.json',
    'data/cs.AI/24/08/26/papers.json'
  ];

  for (const file of knownFiles) {
    try {
      const response = await fetch(file);
      if (response.ok) {
        const fileData = await response.json();

        if (fileData.papers && Array.isArray(fileData.papers)) {
          fileData.papers.forEach(paper => {
            const formattedPaper = {
              id: paper.id,
              abs_url: paper.abs_url,
              title: paper.title,
              summary: paper.summary,
              published: paper.published ? paper.published.split('T')[0] : 'Unknown',
              authors: paper.authors || [],
              categories: paper.categories || [],
              affiliations: paper.affiliations || { has_notable_affiliations: false },
              notable_affiliations_list: []
            };

            if (paper.affiliations && paper.affiliations.notable_affiliations) {
              const paperAffiliations = new Set();
              paper.affiliations.notable_affiliations.forEach(affiliation => {
                if (affiliation.organization) {
                  paperAffiliations.add(affiliation.organization);
                  allNotableAffiliations.add(affiliation.organization);
                }
              });
              formattedPaper.notable_affiliations_list = Array.from(paperAffiliations);
            }

            if (paper.categories) {
              paper.categories.forEach(cat => allCategories.add(cat));
            }

            allPapers.push(formattedPaper);
          });
        }
      }
    } catch (error) {
      console.warn(`Failed to load ${file}:`, error);
    }
  }

  console.log(`Loaded ${allPapers.length} papers`);
  initializeUI();
  renderPapers();
}

// Dark mode functionality
function initializeTheme() {
  const themeToggle = document.getElementById('themeToggle');
  const themeIcon = document.getElementById('themeIcon');
  const themeText = document.getElementById('themeText');

  // Check for saved theme preference or default to light mode
  const savedTheme = localStorage.getItem('theme') || 'light';
  document.documentElement.setAttribute('data-theme', savedTheme);
  updateThemeUI(savedTheme, themeIcon, themeText);

  themeToggle.addEventListener('click', () => {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    updateThemeUI(newTheme, themeIcon, themeText);
  });
}

function updateThemeUI(theme, themeIcon, themeText) {
  if (theme === 'dark') {
    themeIcon.textContent = '☀️';
    themeText.textContent = 'Light Mode';
  } else {
    themeIcon.textContent = '🌙';
    themeText.textContent = 'Dark Mode';
  }
}

// Initialize UI elements after data is loaded
function initializeUI() {
  // Initialize theme first
  initializeTheme();

  // Populate category dropdown
  const categoryFilter = document.getElementById('categoryFilter');
  // Clear existing options except the first one
  categoryFilter.innerHTML = '<option value="">All categories</option>';

  Array.from(allCategories).sort().forEach(cat => {
    const opt = document.createElement('option');
    opt.value = cat;
    opt.textContent = cat;
    categoryFilter.appendChild(opt);
  });
}

function renderPapers() {
  const list = document.getElementById('paperList');
  list.innerHTML = '';
  const search = document.getElementById('searchInput').value.toLowerCase();
  const date = document.getElementById('dateFilter').value;
  const cat = document.getElementById('categoryFilter').value;
  const notable = document.getElementById('notableFilter').checked;

  const filtered = allPapers.filter(p => {
    if (search) {
      const text = p.title.toLowerCase() + p.authors.join(" ").toLowerCase();
      if (!text.includes(search)) return false;
    }
    if (date && p.published !== date) return false;
    if (cat && !p.categories.includes(cat)) return false;
    if (notable && !p.affiliations.has_notable_affiliations) return false;
    return true;
  });

  filtered.forEach(p => {
    const card = document.createElement('div');
    card.className = 'paper-card';

    const header = document.createElement('div');
    header.className = 'paper-header';

    const title = document.createElement('div');
    title.className = 'paper-title';
    title.textContent = p.title;

    const cats = document.createElement('div');
    p.categories.forEach(c => {
      const b = document.createElement('span');
      b.className = 'badge';
      b.textContent = c;
      cats.appendChild(b);
    });

    if (p.affiliations.has_notable_affiliations) {
      const n = document.createElement('span');
      n.className = 'badge notable';
      n.textContent = 'Notable Authors';
      cats.appendChild(n);
    }

    const d = document.createElement('span');
    d.className = 'badge date-badge';
    d.textContent = p.published;
    cats.appendChild(d);

    header.appendChild(title);
    header.appendChild(cats);
    card.appendChild(header);

    const summary = document.createElement('div');
    summary.className = 'paper-summary';
    summary.textContent = p.summary;
    card.appendChild(summary);

    const authors = document.createElement('div');
    authors.className = 'paper-meta';
    authors.innerHTML = `<strong>Authors: <AUTHORS>
    card.appendChild(authors);

    const categories = document.createElement('div');
    categories.className = 'paper-meta';
    categories.innerHTML = `<strong>Categories:</strong> ${p.categories.join(', ')}`;
    card.appendChild(categories);

    // Add notable affiliations if they exist
    if (p.notable_affiliations_list && p.notable_affiliations_list.length > 0) {
      const affiliations = document.createElement('div');
      affiliations.className = 'paper-meta notable-affiliations';
      affiliations.innerHTML = `<strong>Notable Affiliations:</strong> ${p.notable_affiliations_list.join(', ')}`;
      card.appendChild(affiliations);
    }

    const link = document.createElement('div');
    link.className = 'paper-link';
    link.innerHTML = `<a href="${p.abs_url}" target="_blank">View on arXiv</a>`;
    card.appendChild(link);

    list.appendChild(card);
  });
}

// Event listeners
document.getElementById('searchInput').addEventListener('input', renderPapers);
document.getElementById('dateFilter').addEventListener('change', renderPapers);
document.getElementById('categoryFilter').addEventListener('change', renderPapers);
document.getElementById('notableFilter').addEventListener('change', renderPapers);

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM loaded, initializing application...');
  // Try to load data with JSON file list first, fallback to direct file loading
  loadPaperData().catch((error) => {
    console.log('Primary data loading failed:', error);
    console.log('Falling back to direct file loading...');
    loadPaperDataFallback();
  });
});
</script>

</body>
</html>
