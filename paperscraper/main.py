#!/usr/bin/env python3
"""
CLI to work with arXiv papers: fetch metadata, cache first pages, and extract affiliations.

This module also serves as the entrypoint for the `paperscraper` console script.
"""
from __future__ import annotations

import argparse
import json
import logging
import os
import sys
import time
from typing import List, Optional

DEFAULT_DATA_DIR = "data"


def configure_logging(verbosity: int) -> None:
    level = logging.WARNING
    if verbosity == 1:
        level = logging.INFO
    elif verbosity >= 2:
        level = logging.DEBUG
    logging.basicConfig(level=level, format="%(levelname)s: %(message)s")



def _date_parts(date: str):
    y, m, d = date.split("-")
    return y[2:], m, d


def _category_day_dir(root: str, category: str, date: str) -> str:
    """Function for category-based structure"""
    yy, mm, dd = _date_parts(date)
    return os.path.join(root, category, yy, mm, dd)


def _papers_json_path(root: str, category: str, date: str) -> str:
    """Get path to unified papers.json file"""
    return os.path.join(_category_day_dir(root, category, date), "papers.json")


def _load_papers_json(papers_path: str) -> dict:
    """Load existing papers.json or return empty structure"""
    if os.path.exists(papers_path):
        try:
            with open(papers_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logging.warning("Failed to load existing papers.json: %s", e)
    return {"papers": []}


def _save_papers_json(papers_path: str, data: dict) -> None:
    """Save papers.json with proper structure"""
    os.makedirs(os.path.dirname(papers_path), exist_ok=True)
    with open(papers_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


def _dedupe_papers(existing_papers: List[dict], new_papers: List[dict]) -> List[dict]:
    """Deduplicate papers based on abs_url or id"""
    seen = set()
    for paper in existing_papers:
        key = paper.get("abs_url") or paper.get("id")
        if key:
            seen.add(str(key))
    
    deduped = []
    for paper in new_papers:
        key = paper.get("abs_url") or paper.get("id")
        if key and str(key) not in seen:
            deduped.append(paper)
            seen.add(str(key))
    
    return deduped


def _fetch_metadata_for_category(category: str, date: str, limit: int = None) -> List[dict]:
    """Fetch metadata for any arXiv category"""
    from paperscraper.metadata import fetch_metadata_for_category
    return fetch_metadata_for_category(category, date, limit=limit)


def cmd_collect(args: argparse.Namespace) -> int:
    """Unified command that handles metadata, pages, and affiliations in one go"""
    category = args.category
    date = args.date
    data_dir = args.data_dir
    steps = args.steps or ["metadata", "pages", "affiliations"]
    
    papers_path = _papers_json_path(data_dir, category, date)
    
    # Load existing papers
    papers_data = _load_papers_json(papers_path)
    existing_papers = papers_data.get("papers", [])
    
    # Step 1: Metadata
    if "metadata" in steps:
        logging.info("Step 1/3: Fetching metadata for %s on %s", category, date)
        try:
            new_papers = _fetch_metadata_for_category(category, date, limit=args.num)
        except ValueError as e:
            print(f"Error: {e}", file=sys.stderr)
            return 2
        except Exception as e:
            print(f"Network/API error: {e}", file=sys.stderr)
            return 4
        
        if not new_papers and not existing_papers:
            print("No papers found for that date.")
            return 3
        
        # Deduplicate and merge
        deduped_new = _dedupe_papers(existing_papers, new_papers)
        all_papers = existing_papers + deduped_new
        
        # Update papers data
        papers_data = {
            "date": date,
            "category": category,
            "count": len(all_papers),
            "papers": all_papers
        }
        
        # Save updated data
        try:
            _save_papers_json(papers_path, papers_data)
            logging.info("Added %d new papers (%d total)", len(deduped_new), len(all_papers))
        except Exception as e:
            print(f"Failed to save papers.json: {e}", file=sys.stderr)
            return 5
    
    # Reload papers data for subsequent steps
    papers_data = _load_papers_json(papers_path)
    papers = papers_data.get("papers", [])
    
    if not papers:
        print("No papers available to process.")
        return 3
    
    # Step 2: Pages
    if "pages" in steps:
        logging.info("Step 2/3: Fetching first page content")
        from paperscraper.page import getFirstPageText
        
        updated = False
        for i, paper in enumerate(papers):
            if paper.get("first_page_text"):
                continue  # Skip if already has page text
            
            logging.info("Processing page %d/%d: %s", i+1, len(papers), paper.get("title", "(untitled)"))
            
            try:
                page_info = getFirstPageText(paper)
                if page_info.get("text"):
                    paper["first_page_text"] = page_info["text"]
                    paper["first_page_source"] = page_info.get("source", "pdf")
                    updated = True
                else:
                    logging.warning("Empty page text for paper: %s", paper.get("title", ""))
            except Exception as e:
                logging.error("Failed to get page text for %s: %s", paper.get("title", ""), e)
        
        if updated:
            try:
                _save_papers_json(papers_path, papers_data)
                logging.info("Updated papers with first page content")
            except Exception as e:
                print(f"Failed to save updated papers.json: {e}", file=sys.stderr)
                return 5
    
    # Step 3: Affiliations
    if "affiliations" in steps:
        logging.info("Step 3/3: Processing affiliations")
        from paperscraper.llm import getAffiliationsBatch

        # Find papers that need affiliation processing
        papers_to_process = []
        paper_indices = []

        for i, paper in enumerate(papers):
            if not paper.get("affiliations"):
                papers_to_process.append(paper)
                paper_indices.append(i)

        if papers_to_process:
            logging.info("Processing affiliations for %d papers using batch processing", len(papers_to_process))

            try:
                batch_start = time.time()
                batch_results, effective_max_workers = getAffiliationsBatch(
                    papers_to_process,
                    provider=args.provider,
                    api_key=args.api_key,
                    base_url=args.base_url,
                    model=args.model,
                    max_workers=getattr(args, 'max_workers', None),
                )
                batch_end = time.time()

                # Log the effective max_workers used
                max_workers_arg = getattr(args, 'max_workers', None)
                if max_workers_arg is None:
                    cpu_count = os.cpu_count()
                    logging.info("Batch processing completed using %d workers (auto-detected from %d CPU cores)",
                               effective_max_workers, cpu_count or 0)
                else:
                    logging.info("Batch processing completed using %d workers (user-specified)", effective_max_workers)

                # Apply results back to original papers
                updated = False
                for result, paper_idx in zip(batch_results, paper_indices):
                    paper = papers[paper_idx]

                    if result.get("error"):
                        logging.error("Failed to get affiliations for %s: %s",
                                    paper.get("title", ""), result["error"])
                    else:
                        paper["affiliations"] = result.get("affiliations")
                        paper["input_tokens"] = result.get("input_tokens", 0)
                        paper["output_tokens"] = result.get("output_tokens", 0)
                        # Store batch processing time info instead of individual timing
                        paper["batch_processing_time"] = round(batch_end - batch_start, 2)
                        paper["batch_size"] = len(papers_to_process)
                        paper["max_workers_used"] = effective_max_workers
                        updated = True

                if updated:
                    logging.info("Batch processing completed in %.2f seconds", batch_end - batch_start)

            except Exception as e:
                logging.error("Failed to process affiliations batch: %s", e)
                print(f"Batch processing failed: {e}", file=sys.stderr)
                return 5
        else:
            logging.info("All papers already have affiliations")

        # Save updated papers if any changes were made
        if papers_to_process:
            try:
                _save_papers_json(papers_path, papers_data)
                logging.info("Updated papers with affiliations")
            except Exception as e:
                print(f"Failed to save updated papers.json: {e}", file=sys.stderr)
                return 5
    
    # Print summary
    print(f"Processing complete! Results saved to: {papers_path}")
    print(f"Total papers: {len(papers)}")

    # Print metrics and save as JSON
    if papers:
        # Count papers with different types of data
        papers_with_pages = sum(1 for p in papers if p.get("first_page_text"))
        papers_with_affiliations = sum(1 for p in papers if p.get("affiliations"))

        print(f"\n=== Metrics ===")
        print(f"Papers with first page content: {papers_with_pages}/{len(papers)} ({papers_with_pages/len(papers)*100:.1f}%)")
        print(f"Papers with affiliations: {papers_with_affiliations}/{len(papers)} ({papers_with_affiliations/len(papers)*100:.1f}%)")

        # Token usage metrics if available
        if papers_with_affiliations > 0:
            total_input_tokens = sum(p.get("input_tokens", 0) for p in papers if p.get("affiliations"))
            total_output_tokens = sum(p.get("output_tokens", 0) for p in papers if p.get("affiliations"))
            total_tokens = total_input_tokens + total_output_tokens

            if total_tokens > 0:
                print(f"Token usage: {total_input_tokens:,} input + {total_output_tokens:,} output = {total_tokens:,} total")

        # Notable affiliations summary
        papers_with_notable = 0
        if papers_with_affiliations > 0:
            papers_with_notable = sum(1 for p in papers
                                    if p.get("affiliations", {}).get("has_notable_affiliations", False))
            if papers_with_notable > 0:
                print(f"Papers with notable affiliations: {papers_with_notable}/{papers_with_affiliations} ({papers_with_notable/papers_with_affiliations*100:.1f}%)")

        # Processing time summary if available
        batch_times = [p.get("batch_processing_time") for p in papers if p.get("batch_processing_time")]
        batch_sizes = [p.get("batch_size") for p in papers if p.get("batch_size")]
        max_workers_values = [p.get("max_workers_used") for p in papers if p.get("max_workers_used")]

        if batch_times and batch_sizes:
            # All papers in a batch have the same batch_processing_time and batch_size
            total_batch_time = batch_times[0]  # They're all the same
            batch_size = batch_sizes[0]  # They're all the same

            # Show max_workers info if available
            if max_workers_values:
                max_workers_used = max_workers_values[0]  # They're all the same
                print(f"Batch processing time: {total_batch_time:.2f}s total ({total_batch_time/batch_size:.2f}s per paper if sequential, max_workers: {max_workers_used})")
            else:
                print(f"Batch processing time: {total_batch_time:.2f}s total ({total_batch_time/batch_size:.2f}s per paper if sequential)")
        else:
            # Check for legacy individual processing times
            individual_times = [p.get("affiliation_processing_time", 0) for p in papers if p.get("affiliation_processing_time")]
            if individual_times:
                avg_time = sum(individual_times) / len(individual_times)
                print(f"Average affiliation processing time: {avg_time:.2f}s per paper")

        # Save comprehensive metrics as JSON if we have papers with affiliations
        if papers_with_affiliations > 0:
            try:
                from paperscraper.metrics import collectMetricsData, saveMetricsAsJson

                # Prepare affiliation data for metrics calculation
                affiliations_data = []
                for paper in papers:
                    if paper.get("affiliations"):
                        # Create affiliation record with all required metrics fields
                        affiliation_record = {
                            "input_tokens": paper.get("input_tokens", 0),
                            "output_tokens": paper.get("output_tokens", 0),
                            "total_processing_time": paper.get("total_processing_time", 0.0),
                            "first_page_processing_time": paper.get("first_page_processing_time", 0.0),
                            "affiliation_processing_time": paper.get("affiliation_processing_time", 0.0),
                            "paper_source": paper.get("paper_source", "none")
                        }
                        affiliations_data.append(affiliation_record)

                if affiliations_data:
                    # Collect comprehensive metrics
                    metrics_data = collectMetricsData(affiliations_data)

                    # Add additional summary metrics from the current processing
                    metrics_data["summary"] = {
                        "total_papers": len(papers),
                        "papers_with_pages": papers_with_pages,
                        "papers_with_affiliations": papers_with_affiliations,
                        "papers_with_notable_affiliations": papers_with_notable,
                        "date": date,
                        "category": category
                    }

                    # Add batch processing metrics if available
                    if batch_times and batch_sizes:
                        metrics_data["batch_processing"] = {
                            "total_batch_time": batch_times[0],
                            "batch_size": batch_sizes[0],
                            "max_workers_used": max_workers_values[0] if max_workers_values else None
                        }

                    # Save metrics to JSON file
                    metrics_path = saveMetricsAsJson(metrics_data, data_dir, category, date)
                    print(f"Metrics saved to: {metrics_path}")

            except Exception as e:
                logging.warning("Failed to save metrics JSON: %s", e)
    
    return 0




def build_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(description="Collect and visualize arXiv papers: metadata, pages, and affiliations.")

    # Add subcommands
    subparsers = p.add_subparsers(dest='command', help='Available commands')

    # Collect command (default behavior)
    collect_parser = subparsers.add_parser('collect', help='Collect papers (metadata, pages, affiliations)')
    collect_parser.add_argument("category", help="arXiv category (e.g., cs.AI, cs.CV, cs.CL, cs.LG, math.CO, physics.optics)")
    collect_parser.add_argument("date", help="Date in YYYY-MM-DD format")
    
    collect_parser.add_argument("--data-dir", default=DEFAULT_DATA_DIR, help="Root data directory (default: data)")
    collect_parser.add_argument("--verbose", "-v", action="count", default=0, help="Increase logging verbosity (-v, -vv)")

    # Processing options
    collect_parser.add_argument("--steps", help="Comma-separated steps: metadata,pages,affiliations (default: all)")
    collect_parser.add_argument("--num", type=int, help="Max number of papers to fetch (if not specified, will prompt for confirmation to process all)")
    collect_parser.add_argument("-y", "--yes", action="store_true", help="Auto-confirm prompts (skip confirmation for processing all papers)")

    # LLM options for affiliations step
    collect_parser.add_argument("--provider", choices=["openai", "ollama"], default="openai", help="LLM provider")
    collect_parser.add_argument("--api-key", help="API key (ignored for ollama; default env vars used)")
    collect_parser.add_argument("--base-url", help="Override base URL (optional)")
    collect_parser.add_argument("--model", help="Model name (default via AFFILIATIONS_MODEL env var)")
    collect_parser.add_argument("--max-workers", type=int, help="Max worker threads for batch processing (default: CPU cores - 1)")

    # Visualize command
    visualize_parser = subparsers.add_parser('visualize', help='Launch web visualization of collected papers')
    visualize_parser.add_argument("--data-dir", default=DEFAULT_DATA_DIR, help="Root data directory (default: data)")
    visualize_parser.add_argument("--port", type=int, help="Port for web server (default: auto-detect)")
    visualize_parser.add_argument("--no-browser", action="store_true", help="Don't automatically open browser")
    visualize_parser.add_argument("--verbose", "-v", action="count", default=0, help="Increase logging verbosity (-v, -vv)")

    return p


def main(argv: List[str]) -> int:
    parser = build_parser()

    # Handle backward compatibility: if first arg is not a command, assume it's collect
    if argv and argv[0] not in ['collect', 'visualize', '--help', '-h']:
        # Insert 'collect' as the first argument for backward compatibility
        argv = ['collect'] + argv

    args = parser.parse_args(argv)
    configure_logging(getattr(args, 'verbose', 0))

    # Handle different commands
    if args.command == 'visualize':
        from paperscraper.visualizer import cmd_visualize
        return cmd_visualize(args.data_dir, args.port, args.no_browser)
    elif args.command == 'collect' or args.command is None:
        # Default to collect command for backward compatibility
        return handle_collect_command(args)
    else:
        print(f"Unknown command: {args.command}", file=sys.stderr)
        return 1


def handle_collect_command(args: argparse.Namespace) -> int:
    """Handle the collect command logic."""

    # Parse steps if provided
    if args.steps:
        args.steps = [step.strip() for step in args.steps.split(",")]
        # Validate steps
        valid_steps = {"metadata", "pages", "affiliations"}
        for step in args.steps:
            if step not in valid_steps:
                print(f"Invalid step: {step}. Valid steps: {', '.join(valid_steps)}", file=sys.stderr)
                return 2
    else:
        args.steps = ["metadata", "pages", "affiliations"]  # Default to all steps
    
    # Check if user didn't specify num parameter and confirm they want all files
    if args.num is None and not args.yes:
        response = input("No --num parameter specified. Do you want to process ALL papers for this date? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("Operation cancelled. Please specify --num parameter to limit the number of papers.")
            return 1
        # Keep args.num as None to indicate no limit
    elif args.num is None and args.yes:
        # Auto-confirm with -y flag
        print("Auto-confirming: processing ALL papers for this date (--yes flag specified)")
    
    # Check dependencies and add missing steps
    papers_path = _papers_json_path(args.data_dir, args.category, args.date)
    papers_data = _load_papers_json(papers_path)
    existing_papers = papers_data.get("papers", [])
    
    # Check if papers have required data for each step
    has_metadata = len(existing_papers) > 0
    has_pages = has_metadata and any(paper.get("first_page_text") for paper in existing_papers)
    
    steps_to_add = []
    
    # If running affiliations step, ensure metadata and pages are available
    if "affiliations" in args.steps:
        if not has_pages and "pages" not in args.steps:
            steps_to_add.append("pages")
            print("Affiliations step requires page content. Adding 'pages' step...")
        if not has_metadata and "metadata" not in args.steps:
            steps_to_add.append("metadata")
            print("Pages step requires metadata. Adding 'metadata' step...")
    
    # If running pages step, ensure metadata is available
    elif "pages" in args.steps:
        if not has_metadata and "metadata" not in args.steps:
            steps_to_add.append("metadata")
            print("Pages step requires metadata. Adding 'metadata' step...")
    
    # Add missing steps in correct order
    if steps_to_add:
        # Add in reverse order so they appear at the beginning in correct sequence
        for step in reversed(steps_to_add):
            if step not in args.steps:
                args.steps = [step] + args.steps
    
    return cmd_collect(args)


if __name__ == "__main__":
    sys.exit(main(sys.argv[1:]))
