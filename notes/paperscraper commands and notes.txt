

Implement batch processing for affiliations - ThreadPoolExecutor


If running pages , if no such file, then run the metadata step 
If running affiliations, if no such file, then run the metadata and pages steps 

In both cases, if num is not specified then make the user confirm y/n (default N) or pass in a -y command to auto yes




paperscraper cs.AI 2024-08-20 --num 8 --provider ollama --model qwen3:0.6b --max-workers 4 -v 
paperscraper cs.AI 2024-08-13 --num 12 --provider openai --model gpt-5-nano -v



paperscraper cs.AI 2024-08-01 --steps metadata --num 10
paperscraper cs.AI 2024-08-01 --steps pages


split LLM.py into inference.py and models.py models.py should implement a BaseLanguageModel class that has init, with params like model id, temp, etc , and function for infer() that takes in a list of inference parameters - specific to model type, but have messages, model id, and optional response format (structured object), described below. infer() refers to multiple prompts at once, so most children will have an invoke_single_prompt as well OllamaLanguageModel and OpenAILanguageModel should have that as a parent but be two different clases. They should also have variables api_key, model_id, base_url, etc - they will be essentially the same code, but I want to expand the amount of models later so this is a starting point. for each, implement _process_single_prompt() which has params messages, response_format(optional) and infer() just iterates through the list and calls client.beta.chat.completions.parse on it inference.py should take the languagemodel object and format the inference bodies of the inferences and parse the output .




paperscraper cs.AI 2024-08-24 --num 8 --provider ollama --model qwen3:0.6b --max-workers 6 
paperscraper cs.AI 2024-08-25 --num 8 --provider ollama --model qwen3:1.7b --max-workers 6
paperscraper cs.AI 2024-08-26 --num 8 --provider ollama --model qwen3:4b-instruct --max-workers 6