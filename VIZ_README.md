# Paper Visualization

This directory contains a web-based visualization tool for exploring the scraped research papers.

## Files

- `viz.html` - Main visualization interface
- `get_data_files.js` - Node.js script to discover all papers.json files
- `get_data_files.php` - PHP script to discover all papers.json files (alternative)
- `data_files.json` - Generated list of all available paper data files
- `update_data_files.sh` - <PERSON><PERSON><PERSON> to regenerate the data files list
- `test_data.html` - Simple test page to verify data loading

## Features

The visualization provides:

1. **Search and Filter**:
   - Text search across paper titles and authors
   - Filter by publication date
   - Filter by arXiv category
   - Toggle to show only papers with notable affiliations

2. **Dark Mode**:
   - Toggle between light and dark themes
   - Preference is saved in browser localStorage
   - Smooth transitions between themes
   - Responsive design for mobile devices

3. **Paper Information Display**:
   - Paper title and summary
   - Author list
   - arXiv categories
   - Publication date
   - **Notable affiliations** - Shows organizations from papers with notable authors
   - Direct link to arXiv abstract

4. **Notable Affiliations**:
   - Displays a deduplicated list of organizations from the `notable_affiliations` field
   - Only shown for papers that have notable affiliations
   - Helps identify papers from prestigious institutions

## Usage

1. **Start a local server**:
   ```bash
   python3 -m http.server 8000
   ```

2. **Update data files list** (run this when new papers are added):
   ```bash
   ./update_data_files.sh
   ```

3. **Open the visualization**:
   Navigate to `http://localhost:8000/viz.html`

## Data Structure

The visualization expects papers.json files with the following structure:

```json
{
  "date": "2024-08-25",
  "category": "cs.AI", 
  "papers": [
    {
      "id": "paper_id",
      "title": "Paper Title",
      "summary": "Paper summary...",
      "published": "2024-08-25T00:50:33Z",
      "authors": ["Author 1", "Author 2"],
      "categories": ["cs.AI", "cs.LG"],
      "affiliations": {
        "has_notable_affiliations": true,
        "notable_affiliations": [
          {
            "author_name": "Author Name",
            "organization": "Institution Name"
          }
        ]
      }
    }
  ]
}
```

## Technical Notes

- The visualization automatically discovers all `papers.json` files in the `data/` directory
- Falls back to a hardcoded list if the dynamic discovery fails
- Uses vanilla JavaScript with no external dependencies
- Responsive design works on desktop and mobile devices
- Data is loaded asynchronously to handle large datasets efficiently

## Troubleshooting

If papers don't appear:
1. Check browser console for errors
2. Verify `data_files.json` contains the correct file paths
3. Run `./update_data_files.sh` to regenerate the file list
4. Ensure the local server is running and can access the data files
