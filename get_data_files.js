const fs = require('fs');
const path = require('path');

function findJsonFiles(dir, files = []) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
            findJsonFiles(fullPath, files);
        } else if (item === 'papers.json') {
            // Convert to relative path from current directory
            const relativePath = path.relative('.', fullPath);
            files.push(relativePath);
        }
    }
    
    return files;
}

try {
    const dataDir = './data';
    if (fs.existsSync(dataDir)) {
        const jsonFiles = findJsonFiles(dataDir);
        console.log(JSON.stringify(jsonFiles));
    } else {
        console.log('[]');
    }
} catch (error) {
    console.error('Error:', error.message);
    console.log('[]');
}
